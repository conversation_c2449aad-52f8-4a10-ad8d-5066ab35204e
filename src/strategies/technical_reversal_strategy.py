"""
技术反转选股策略
基于601111在2025年4月7日和4月30日买入点特征设计
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict
from statistics import mean

from ..core.interfaces.strategy import ISelectionStrategy
from ..core.interfaces.data_access import IDataAccess


class TechnicalReversalStrategy(ISelectionStrategy):
    """技术反转选股策略"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = {
            # RSI条件 - 14日RSI < 30
            'rsi_max': 35.0,  # RSI最大值
            'rsi_period': 14,  # RSI计算周期

            # 交易量条件 - 最近1-2日成交量 > 前5日平均成交量的1.5~2倍
            'volume_ratio_min': 1.5,  # 相对5日均量最小倍数
            'volume_ratio_max': 2.0,  # 相对5日均量最大倍数
            'volume_check_days': 2,   # 检查最近几日的成交量
            'volume_baseline_days': 5,  # 成交量基准期天数

            # 布林带条件 - 突破布林带下轨
            'bb_breakthrough_threshold': 0.02,  # 突破下轨的阈值(2%)
            'bb_period': 20,  # 布林带计算周期
            'bb_std_multiplier': 2.0,  # 布林带标准差倍数

            # 基本过滤条件
            'min_price': 3.0,  # 最低价格
            'max_price': 50.0,  # 最高价格
            'exclude_st': True,  # 排除ST股票
            'baseline_days': 30,  # 基准期天数（确保有足够数据计算指标）
            'max_results': 20  # 最大结果数量
        }

    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return "技术反转策略"

    def get_strategy_description(self) -> str:
        """获取策略描述"""
        return "基于布林带突破、RSI超卖和成交量放大三个核心指标识别技术反转机会"

    def get_config(self) -> Dict:
        """获取策略配置"""
        return self.config.copy()

    def set_config(self, config: Dict) -> None:
        """设置策略配置"""
        self.config.update(config)

    def validate_config(self, config: Dict) -> bool:
        """验证策略配置"""
        required_keys = [
            'rsi_max', 'rsi_period', 'volume_ratio_min', 'volume_ratio_max',
            'volume_check_days', 'volume_baseline_days', 'bb_breakthrough_threshold',
            'bb_period', 'bb_std_multiplier', 'min_price', 'max_price',
            'baseline_days', 'max_results'
        ]

        for key in required_keys:
            if key not in config:
                return False

        # 验证数值范围
        if config['rsi_max'] <= 0 or config['rsi_max'] > 100:
            return False
        if config['volume_ratio_min'] >= config['volume_ratio_max']:
            return False
        if config['volume_ratio_min'] <= 0:
            return False
        if config['min_price'] <= 0 or config['max_price'] <= config['min_price']:
            return False
        if config['baseline_days'] <= 0:
            return False
        if config['max_results'] <= 0:
            return False
        if config['rsi_period'] <= 0:
            return False
        if config['bb_period'] <= 0:
            return False
        if config['volume_check_days'] <= 0 or config['volume_baseline_days'] <= 0:
            return False

        return True

    def execute(self, data_access: IDataAccess, target_date: datetime = None) -> List[Dict]:
        """执行选股策略

        Args:
            data_access: 数据访问接口
            target_date: 目标日期，如果为None则使用当前日期
        """
        try:
            self.logger.info(f"开始执行{self.get_strategy_name()}")

            # 获取所有股票代码
            stock_codes = data_access.get_all_stock_codes()
            self.logger.info(f"获取到{len(stock_codes)}只股票")

            # 计算日期范围 - 使用固定的目标日期确保结果一致性
            if target_date is None:
                end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                end_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)

            start_date = end_date - timedelta(days=self.config['baseline_days'] + 10)  # 多取几天以防节假日

            self.logger.info(f"数据查询范围: {start_date.date()} 到 {end_date.date()}")

            results = []
            processed_count = 0

            for stock_code in stock_codes:
                try:
                    # 获取股票基本信息
                    stock_info = data_access.get_stock_info(stock_code)
                    if not stock_info:
                        continue

                    # 过滤ST股票
                    if self.config['exclude_st'] and self._is_st_stock(stock_info['stock_name']):
                        continue

                    # 获取交易数据
                    trading_data = data_access.get_stock_data(stock_code, start_date, end_date)
                    if len(trading_data) < self.config['baseline_days']:
                        continue

                    # 分析技术反转信号
                    reversal_result = self._analyze_technical_reversal(stock_code, stock_info, trading_data, end_date)
                    if reversal_result:
                        results.append(reversal_result)

                    processed_count += 1
                    if processed_count % 100 == 0:
                        self.logger.info(f"已处理{processed_count}只股票，当前选中{len(results)}只")

                except Exception as e:
                    self.logger.warning(f"处理股票{stock_code}时出错: {str(e)}")
                    continue

            # 按评分排序并限制结果数量
            results.sort(key=lambda x: x['score'], reverse=True)
            results = results[:self.config['max_results']]

            self.logger.info(f"选股完成，共选中{len(results)}只股票")
            return results

        except Exception as e:
            self.logger.error(f"执行选股策略失败: {str(e)}")
            raise

    def _is_st_stock(self, stock_name: str) -> bool:
        """判断是否为ST股票"""
        st_keywords = ['ST', '*ST', 'PT']
        return any(keyword in stock_name for keyword in st_keywords)

    def _analyze_technical_reversal(self, stock_code: str, stock_info: Dict, trading_data: List[Dict], target_date: datetime = None) -> Dict:
        """分析技术反转信号"""
        try:
            # 按日期排序
            trading_data.sort(key=lambda x: x['trade_date'])

            # 确保有足够的数据
            if len(trading_data) < self.config['baseline_days']:
                return None

            # 计算技术指标
            df_data = self._calculate_technical_indicators(trading_data)

            # 获取最新交易日数据
            latest_data = df_data[-1]

            # 基本价格过滤
            close_price = float(latest_data['close_price'])
            if close_price < self.config['min_price'] or close_price > self.config['max_price']:
                return None

            # 检查三个核心条件
            conditions = []
            score_components = []

            # 1. RSI条件 - 14日RSI < 30
            rsi = latest_data['rsi']
            if rsi < self.config['rsi_max']:
                conditions.append(f"RSI({rsi:.1f})<30超卖")
                score_components.append(35)
            else:
                return None

            # 2. 布林带条件 - 突破布林带下轨
            bb_lower = latest_data['bb_lower']
            bb_breakthrough = self._check_bollinger_breakthrough(df_data)
            if bb_breakthrough:
                conditions.append(f"突破布林带下轨")
                score_components.append(35)
            else:
                return None

            # 3. 交易量条件 - 最近1-2日成交量 > 前5日平均成交量的1.5~2倍
            volume_amplified = self._check_volume_amplification(df_data)
            if volume_amplified:
                volume_ratio = latest_data['volume_ratio_5']
                conditions.append(f"成交量放大{volume_ratio:.2f}倍")
                score_components.append(30)
            else:
                return None

            # 计算综合评分
            base_score = sum(score_components)

            # 额外加分项
            bonus_score = 0

            # RSI越低加分越多
            if rsi < 25:
                bonus_score += (25 - rsi) * 2
            elif rsi < 30:
                bonus_score += (30 - rsi) * 1

            # 成交量倍数在理想范围内加分
            volume_ratio = latest_data['volume_ratio_5']
            if 1.7 <= volume_ratio <= 1.9:
                bonus_score += 5

            total_score = base_score + bonus_score

            # 生成选股原因
            reason = f"技术反转信号: {', '.join(conditions)}"

            # 使用目标日期确保结果一致性
            if target_date is None:
                selection_date = datetime.now().date()
            else:
                selection_date = target_date.date()

            return {
                'stock_code': stock_code,
                'stock_name': stock_info['stock_name'],
                'score': round(total_score, 2),
                'reason': reason,
                'selection_date': selection_date,
                'close_price': close_price,
                'rsi': rsi,
                'volume_ratio': volume_ratio,
                'bb_lower': bb_lower,
                'bb_breakthrough': bb_breakthrough,
                'conditions_count': len(conditions)
            }

        except Exception as e:
            self.logger.warning(f"分析股票{stock_code}技术反转信号失败: {str(e)}")
            return None

    def _check_bollinger_breakthrough(self, df_data: List[Dict]) -> bool:
        """检查是否突破布林带下轨"""
        try:
            if len(df_data) < 2:
                return False

            latest_data = df_data[-1]

            # 当前价格突破下轨（价格低于下轨一定幅度）
            current_price = latest_data['close_price']
            bb_lower = latest_data['bb_lower']

            # 突破条件：当前收盘价低于布林带下轨
            breakthrough = current_price <= bb_lower * (1 + self.config['bb_breakthrough_threshold'])

            return breakthrough

        except Exception as e:
            self.logger.warning(f"检查布林带突破失败: {str(e)}")
            return False

    def _check_volume_amplification(self, df_data: List[Dict]) -> bool:
        """检查成交量是否放大"""
        try:
            if len(df_data) < self.config['volume_baseline_days'] + self.config['volume_check_days']:
                return False

            # 检查最近1-2日的成交量
            recent_volumes = []
            for i in range(self.config['volume_check_days']):
                recent_volumes.append(df_data[-(i+1)]['volume'])

            # 计算前5日平均成交量
            baseline_volumes = []
            start_idx = -(self.config['volume_check_days'] + self.config['volume_baseline_days'])
            end_idx = -self.config['volume_check_days']

            for i in range(start_idx, end_idx):
                baseline_volumes.append(df_data[i]['volume'])

            avg_baseline_volume = sum(baseline_volumes) / len(baseline_volumes)

            # 检查是否有任何一天的成交量满足放大条件
            for volume in recent_volumes:
                volume_ratio = volume / avg_baseline_volume
                if self.config['volume_ratio_min'] <= volume_ratio <= self.config['volume_ratio_max']:
                    return True

            return False

        except Exception as e:
            self.logger.warning(f"检查成交量放大失败: {str(e)}")
            return False

    def _calculate_technical_indicators(self, trading_data: List[Dict]) -> List[Dict]:
        """计算技术指标"""
        # 转换数据格式
        data = []
        for item in trading_data:
            data.append({
                'trade_date': item['trade_date'],
                'open_price': float(item['open_price']),
                'high_price': float(item['high_price']),
                'low_price': float(item['low_price']),
                'close_price': float(item['close_price']),
                'volume': int(item['volume']),
                'turnover_rate': float(item.get('turnover_rate', 0))
            })

        # 计算5日交易量移动平均（用于成交量放大检查）
        for i in range(len(data)):
            if i >= 4:
                vol_ma5_sum = sum(data[j]['volume'] for j in range(i-4, i+1))
                data[i]['vol_ma5'] = vol_ma5_sum / 5
                data[i]['volume_ratio_5'] = data[i]['volume'] / data[i]['vol_ma5']
            else:
                data[i]['vol_ma5'] = data[i]['volume']
                data[i]['volume_ratio_5'] = 1.0

        # 计算RSI
        rsi_period = self.config['rsi_period']
        for i in range(len(data)):
            if i >= rsi_period:
                gains = []
                losses = []
                for j in range(i-rsi_period+1, i+1):
                    if j > 0:
                        change = data[j]['close_price'] - data[j-1]['close_price']
                        if change > 0:
                            gains.append(change)
                            losses.append(0)
                        else:
                            gains.append(0)
                            losses.append(-change)

                avg_gain = mean(gains) if gains else 0
                avg_loss = mean(losses) if losses else 0

                if avg_loss == 0:
                    data[i]['rsi'] = 100
                else:
                    rs = avg_gain / avg_loss
                    data[i]['rsi'] = 100 - (100 / (1 + rs))
            else:
                data[i]['rsi'] = 50  # 默认值

        # 计算布林带
        bb_period = self.config['bb_period']
        bb_std_multiplier = self.config['bb_std_multiplier']
        for i in range(len(data)):
            if i >= bb_period - 1:
                prices = [data[j]['close_price'] for j in range(i-bb_period+1, i+1)]
                bb_middle = mean(prices)
                variance = sum((p - bb_middle) ** 2 for p in prices) / len(prices)
                bb_std = variance ** 0.5

                data[i]['bb_middle'] = bb_middle
                data[i]['bb_upper'] = bb_middle + (bb_std * bb_std_multiplier)
                data[i]['bb_lower'] = bb_middle - (bb_std * bb_std_multiplier)

                if data[i]['bb_upper'] != data[i]['bb_lower']:
                    data[i]['bb_position'] = (data[i]['close_price'] - data[i]['bb_lower']) / (data[i]['bb_upper'] - data[i]['bb_lower'])
                else:
                    data[i]['bb_position'] = 0.5
            else:
                data[i]['bb_middle'] = data[i]['close_price']
                data[i]['bb_upper'] = data[i]['close_price']
                data[i]['bb_lower'] = data[i]['close_price']
                data[i]['bb_position'] = 0.5

        return data
